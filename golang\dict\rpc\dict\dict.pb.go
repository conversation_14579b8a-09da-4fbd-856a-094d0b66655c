// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: dict.proto

package dict

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典基本信息
type Dict struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,6,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,7,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dict) Reset() {
	*x = Dict{}
	mi := &file_dict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dict) ProtoMessage() {}

func (x *Dict) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dict.ProtoReflect.Descriptor instead.
func (*Dict) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{0}
}

func (x *Dict) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dict) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Dict) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dict) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Dict) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Dict) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *Dict) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 字典分类信息
type DictCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,5,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,6,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictCategory) Reset() {
	*x = DictCategory{}
	mi := &file_dict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictCategory) ProtoMessage() {}

func (x *DictCategory) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictCategory.ProtoReflect.Descriptor instead.
func (*DictCategory) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{1}
}

func (x *DictCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DictCategory) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *DictCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictCategory) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DictCategory) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *DictCategory) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 字典项信息
type DictItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,7,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,8,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictItem) Reset() {
	*x = DictItem{}
	mi := &file_dict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictItem) ProtoMessage() {}

func (x *DictItem) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictItem.ProtoReflect.Descriptor instead.
func (*DictItem) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{2}
}

func (x *DictItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DictItem) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *DictItem) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *DictItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DictItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DictItem) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *DictItem) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 创建字典请求
type CreateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictReq) Reset() {
	*x = CreateDictReq{}
	mi := &file_dict_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictReq) ProtoMessage() {}

func (x *CreateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictReq.ProtoReflect.Descriptor instead.
func (*CreateDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 创建字典响应
type CreateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictResp) Reset() {
	*x = CreateDictResp{}
	mi := &file_dict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictResp) ProtoMessage() {}

func (x *CreateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictResp.ProtoReflect.Descriptor instead.
func (*CreateDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDictResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新字典请求
type UpdateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictReq) Reset() {
	*x = UpdateDictReq{}
	mi := &file_dict_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictReq) ProtoMessage() {}

func (x *UpdateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictReq.ProtoReflect.Descriptor instead.
func (*UpdateDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 更新字典响应
type UpdateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictResp) Reset() {
	*x = UpdateDictResp{}
	mi := &file_dict_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictResp) ProtoMessage() {}

func (x *UpdateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictResp.ProtoReflect.Descriptor instead.
func (*UpdateDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除字典请求
type DeleteDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictReq) Reset() {
	*x = DeleteDictReq{}
	mi := &file_dict_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictReq) ProtoMessage() {}

func (x *DeleteDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictReq.ProtoReflect.Descriptor instead.
func (*DeleteDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除字典响应
type DeleteDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictResp) Reset() {
	*x = DeleteDictResp{}
	mi := &file_dict_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictResp) ProtoMessage() {}

func (x *DeleteDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictResp.ProtoReflect.Descriptor instead.
func (*DeleteDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取字典详情请求
type GetDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictReq) Reset() {
	*x = GetDictReq{}
	mi := &file_dict_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictReq) ProtoMessage() {}

func (x *GetDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictReq.ProtoReflect.Descriptor instead.
func (*GetDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{9}
}

func (x *GetDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典详情响应
type GetDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dict          *Dict                  `protobuf:"bytes,1,opt,name=dict,proto3" json:"dict,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictResp) Reset() {
	*x = GetDictResp{}
	mi := &file_dict_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictResp) ProtoMessage() {}

func (x *GetDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictResp.ProtoReflect.Descriptor instead.
func (*GetDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{10}
}

func (x *GetDictResp) GetDict() *Dict {
	if x != nil {
		return x.Dict
	}
	return nil
}

func (x *GetDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 字典列表请求
type ListDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`      // 模糊查询
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`      // 模糊查询
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"` // -1:全部, 0:禁用, 1:启用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictReq) Reset() {
	*x = ListDictReq{}
	mi := &file_dict_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictReq) ProtoMessage() {}

func (x *ListDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictReq.ProtoReflect.Descriptor instead.
func (*ListDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{11}
}

func (x *ListDictReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ListDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典列表响应
type ListDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Dict                `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictResp) Reset() {
	*x = ListDictResp{}
	mi := &file_dict_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictResp) ProtoMessage() {}

func (x *ListDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictResp.ProtoReflect.Descriptor instead.
func (*ListDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{12}
}

func (x *ListDictResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictResp) GetList() []*Dict {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 创建字典分类请求
type CreateDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        int64                  `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictCategoryReq) Reset() {
	*x = CreateDictCategoryReq{}
	mi := &file_dict_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictCategoryReq) ProtoMessage() {}

func (x *CreateDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictCategoryReq.ProtoReflect.Descriptor instead.
func (*CreateDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{13}
}

func (x *CreateDictCategoryReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *CreateDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 创建字典分类响应
type CreateDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictCategoryResp) Reset() {
	*x = CreateDictCategoryResp{}
	mi := &file_dict_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictCategoryResp) ProtoMessage() {}

func (x *CreateDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictCategoryResp.ProtoReflect.Descriptor instead.
func (*CreateDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{14}
}

func (x *CreateDictCategoryResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDictCategoryResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新字典分类请求
type UpdateDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictCategoryReq) Reset() {
	*x = UpdateDictCategoryReq{}
	mi := &file_dict_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictCategoryReq) ProtoMessage() {}

func (x *UpdateDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictCategoryReq.ProtoReflect.Descriptor instead.
func (*UpdateDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *UpdateDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 更新字典分类响应
type UpdateDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictCategoryResp) Reset() {
	*x = UpdateDictCategoryResp{}
	mi := &file_dict_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictCategoryResp) ProtoMessage() {}

func (x *UpdateDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictCategoryResp.ProtoReflect.Descriptor instead.
func (*UpdateDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateDictCategoryResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateDictCategoryResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除字典分类请求
type DeleteDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictCategoryReq) Reset() {
	*x = DeleteDictCategoryReq{}
	mi := &file_dict_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictCategoryReq) ProtoMessage() {}

func (x *DeleteDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictCategoryReq.ProtoReflect.Descriptor instead.
func (*DeleteDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除字典分类响应
type DeleteDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictCategoryResp) Reset() {
	*x = DeleteDictCategoryResp{}
	mi := &file_dict_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictCategoryResp) ProtoMessage() {}

func (x *DeleteDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictCategoryResp.ProtoReflect.Descriptor instead.
func (*DeleteDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteDictCategoryResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteDictCategoryResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取字典分类详情请求
type GetDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictCategoryReq) Reset() {
	*x = GetDictCategoryReq{}
	mi := &file_dict_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictCategoryReq) ProtoMessage() {}

func (x *GetDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictCategoryReq.ProtoReflect.Descriptor instead.
func (*GetDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{19}
}

func (x *GetDictCategoryReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典分类详情响应
type GetDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Category      *DictCategory          `protobuf:"bytes,1,opt,name=category,proto3" json:"category,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictCategoryResp) Reset() {
	*x = GetDictCategoryResp{}
	mi := &file_dict_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictCategoryResp) ProtoMessage() {}

func (x *GetDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictCategoryResp.ProtoReflect.Descriptor instead.
func (*GetDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{20}
}

func (x *GetDictCategoryResp) GetCategory() *DictCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *GetDictCategoryResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 字典分类列表请求
type ListDictCategoryReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                    // 模糊查询
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`               // -1:全部, 0:禁用, 1:启用
	DictId        int64                  `protobuf:"varint,5,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"` // 指定字典ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictCategoryReq) Reset() {
	*x = ListDictCategoryReq{}
	mi := &file_dict_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictCategoryReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictCategoryReq) ProtoMessage() {}

func (x *ListDictCategoryReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictCategoryReq.ProtoReflect.Descriptor instead.
func (*ListDictCategoryReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{21}
}

func (x *ListDictCategoryReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictCategoryReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictCategoryReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictCategoryReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListDictCategoryReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

// 字典分类列表响应
type ListDictCategoryResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*DictCategory        `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictCategoryResp) Reset() {
	*x = ListDictCategoryResp{}
	mi := &file_dict_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictCategoryResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictCategoryResp) ProtoMessage() {}

func (x *ListDictCategoryResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictCategoryResp.ProtoReflect.Descriptor instead.
func (*ListDictCategoryResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{22}
}

func (x *ListDictCategoryResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictCategoryResp) GetList() []*DictCategory {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDictCategoryResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 创建字典项请求
type CreateDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        int64                  `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictItemReq) Reset() {
	*x = CreateDictItemReq{}
	mi := &file_dict_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictItemReq) ProtoMessage() {}

func (x *CreateDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictItemReq.ProtoReflect.Descriptor instead.
func (*CreateDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{23}
}

func (x *CreateDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *CreateDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CreateDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 创建字典项响应
type CreateDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictItemResp) Reset() {
	*x = CreateDictItemResp{}
	mi := &file_dict_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictItemResp) ProtoMessage() {}

func (x *CreateDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictItemResp.ProtoReflect.Descriptor instead.
func (*CreateDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{24}
}

func (x *CreateDictItemResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新字典项请求
type UpdateDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictItemReq) Reset() {
	*x = UpdateDictItemReq{}
	mi := &file_dict_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictItemReq) ProtoMessage() {}

func (x *UpdateDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictItemReq.ProtoReflect.Descriptor instead.
func (*UpdateDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *UpdateDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UpdateDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 更新字典项响应
type UpdateDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictItemResp) Reset() {
	*x = UpdateDictItemResp{}
	mi := &file_dict_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictItemResp) ProtoMessage() {}

func (x *UpdateDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictItemResp.ProtoReflect.Descriptor instead.
func (*UpdateDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{26}
}

func (x *UpdateDictItemResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除字典项请求
type DeleteDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictItemReq) Reset() {
	*x = DeleteDictItemReq{}
	mi := &file_dict_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictItemReq) ProtoMessage() {}

func (x *DeleteDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictItemReq.ProtoReflect.Descriptor instead.
func (*DeleteDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除字典项响应
type DeleteDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictItemResp) Reset() {
	*x = DeleteDictItemResp{}
	mi := &file_dict_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictItemResp) ProtoMessage() {}

func (x *DeleteDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictItemResp.ProtoReflect.Descriptor instead.
func (*DeleteDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteDictItemResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取字典项详情请求
type GetDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictItemReq) Reset() {
	*x = GetDictItemReq{}
	mi := &file_dict_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictItemReq) ProtoMessage() {}

func (x *GetDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictItemReq.ProtoReflect.Descriptor instead.
func (*GetDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{29}
}

func (x *GetDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典项详情响应
type GetDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *DictItem              `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictItemResp) Reset() {
	*x = GetDictItemResp{}
	mi := &file_dict_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictItemResp) ProtoMessage() {}

func (x *GetDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictItemResp.ProtoReflect.Descriptor instead.
func (*GetDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{30}
}

func (x *GetDictItemResp) GetItem() *DictItem {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *GetDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 字典项列表请求
type ListDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	DictId        int64                  `protobuf:"varint,3,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`             // 指定字典ID
	CategoryId    int64                  `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"` // 指定分类ID
	Code          string                 `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`                                // 模糊查询
	Name          string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                                // 模糊查询
	Status        int32                  `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`                           // -1:全部, 0:禁用, 1:启用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictItemReq) Reset() {
	*x = ListDictItemReq{}
	mi := &file_dict_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictItemReq) ProtoMessage() {}

func (x *ListDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictItemReq.ProtoReflect.Descriptor instead.
func (*ListDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{31}
}

func (x *ListDictItemReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictItemReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *ListDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ListDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ListDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典项列表响应
type ListDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*DictItem            `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictItemResp) Reset() {
	*x = ListDictItemResp{}
	mi := &file_dict_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictItemResp) ProtoMessage() {}

func (x *ListDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictItemResp.ProtoReflect.Descriptor instead.
func (*ListDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{32}
}

func (x *ListDictItemResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictItemResp) GetList() []*DictItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_dict_proto protoreflect.FileDescriptor

const file_dict_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"dict.proto\x12\x04dict\"\xb4\x01\n" +
	"\x04Dict\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12!\n" +
	"\fcreated_time\x18\x06 \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\a \x01(\tR\vupdatedTime\"\xa9\x01\n" +
	"\fDictCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12!\n" +
	"\fcreated_time\x18\x05 \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\x06 \x01(\tR\vupdatedTime\"\xda\x01\n" +
	"\bDictItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12!\n" +
	"\fcreated_time\x18\a \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\b \x01(\tR\vupdatedTime\"g\n" +
	"\rCreateDictReq\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x03 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\":\n" +
	"\x0eCreateDictResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"w\n" +
	"\rUpdateDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"D\n" +
	"\x0eUpdateDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1f\n" +
	"\rDeleteDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"D\n" +
	"\x0eDeleteDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1c\n" +
	"\n" +
	"GetDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"G\n" +
	"\vGetDictResp\x12\x1e\n" +
	"\x04dict\x18\x01 \x01(\v2\n" +
	".dict.DictR\x04dict\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"~\n" +
	"\vListDictReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"^\n" +
	"\fListDictResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\x1e\n" +
	"\x04list\x18\x02 \x03(\v2\n" +
	".dict.DictR\x04list\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\\\n" +
	"\x15CreateDictCategoryReq\x12\x17\n" +
	"\adict_id\x18\x01 \x01(\x03R\x06dictId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x03 \x01(\x05R\x06status\"B\n" +
	"\x16CreateDictCategoryResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"l\n" +
	"\x15UpdateDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\"L\n" +
	"\x16UpdateDictCategoryResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"'\n" +
	"\x15DeleteDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"L\n" +
	"\x16DeleteDictCategoryResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"$\n" +
	"\x12GetDictCategoryReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"_\n" +
	"\x13GetDictCategoryResp\x12.\n" +
	"\bcategory\x18\x01 \x01(\v2\x12.dict.DictCategoryR\bcategory\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x8b\x01\n" +
	"\x13ListDictCategoryReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\x12\x17\n" +
	"\adict_id\x18\x05 \x01(\x03R\x06dictId\"n\n" +
	"\x14ListDictCategoryResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12&\n" +
	"\x04list\x18\x02 \x03(\v2\x12.dict.DictCategoryR\x04list\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\"\x8d\x01\n" +
	"\x11CreateDictItemReq\x12\x17\n" +
	"\adict_id\x18\x01 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\">\n" +
	"\x12CreateDictItemResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x9d\x01\n" +
	"\x11UpdateDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\"H\n" +
	"\x12UpdateDictItemResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"#\n" +
	"\x11DeleteDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"\x12DeleteDictItemResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\" \n" +
	"\x0eGetDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"O\n" +
	"\x0fGetDictItemResp\x12\"\n" +
	"\x04item\x18\x01 \x01(\v2\x0e.dict.DictItemR\x04item\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xbc\x01\n" +
	"\x0fListDictItemReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x17\n" +
	"\adict_id\x18\x03 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x04 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x05 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\a \x01(\x05R\x06status\"f\n" +
	"\x10ListDictItemResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\"\n" +
	"\x04list\x18\x02 \x03(\v2\x0e.dict.DictItemR\x04list\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage2\xf1\a\n" +
	"\x11DictSystemService\x127\n" +
	"\n" +
	"CreateDict\x12\x13.dict.CreateDictReq\x1a\x14.dict.CreateDictResp\x127\n" +
	"\n" +
	"UpdateDict\x12\x13.dict.UpdateDictReq\x1a\x14.dict.UpdateDictResp\x127\n" +
	"\n" +
	"DeleteDict\x12\x13.dict.DeleteDictReq\x1a\x14.dict.DeleteDictResp\x12.\n" +
	"\aGetDict\x12\x10.dict.GetDictReq\x1a\x11.dict.GetDictResp\x121\n" +
	"\bListDict\x12\x11.dict.ListDictReq\x1a\x12.dict.ListDictResp\x12O\n" +
	"\x12CreateDictCategory\x12\x1b.dict.CreateDictCategoryReq\x1a\x1c.dict.CreateDictCategoryResp\x12O\n" +
	"\x12UpdateDictCategory\x12\x1b.dict.UpdateDictCategoryReq\x1a\x1c.dict.UpdateDictCategoryResp\x12O\n" +
	"\x12DeleteDictCategory\x12\x1b.dict.DeleteDictCategoryReq\x1a\x1c.dict.DeleteDictCategoryResp\x12F\n" +
	"\x0fGetDictCategory\x12\x18.dict.GetDictCategoryReq\x1a\x19.dict.GetDictCategoryResp\x12I\n" +
	"\x10ListDictCategory\x12\x19.dict.ListDictCategoryReq\x1a\x1a.dict.ListDictCategoryResp\x12C\n" +
	"\x0eCreateDictItem\x12\x17.dict.CreateDictItemReq\x1a\x18.dict.CreateDictItemResp\x12C\n" +
	"\x0eUpdateDictItem\x12\x17.dict.UpdateDictItemReq\x1a\x18.dict.UpdateDictItemResp\x12C\n" +
	"\x0eDeleteDictItem\x12\x17.dict.DeleteDictItemReq\x1a\x18.dict.DeleteDictItemResp\x12:\n" +
	"\vGetDictItem\x12\x14.dict.GetDictItemReq\x1a\x15.dict.GetDictItemResp\x12=\n" +
	"\fListDictItem\x12\x15.dict.ListDictItemReq\x1a\x16.dict.ListDictItemRespB\bZ\x06./dictb\x06proto3"

var (
	file_dict_proto_rawDescOnce sync.Once
	file_dict_proto_rawDescData []byte
)

func file_dict_proto_rawDescGZIP() []byte {
	file_dict_proto_rawDescOnce.Do(func() {
		file_dict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dict_proto_rawDesc), len(file_dict_proto_rawDesc)))
	})
	return file_dict_proto_rawDescData
}

var file_dict_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_dict_proto_goTypes = []any{
	(*Dict)(nil),                   // 0: dict.Dict
	(*DictCategory)(nil),           // 1: dict.DictCategory
	(*DictItem)(nil),               // 2: dict.DictItem
	(*CreateDictReq)(nil),          // 3: dict.CreateDictReq
	(*CreateDictResp)(nil),         // 4: dict.CreateDictResp
	(*UpdateDictReq)(nil),          // 5: dict.UpdateDictReq
	(*UpdateDictResp)(nil),         // 6: dict.UpdateDictResp
	(*DeleteDictReq)(nil),          // 7: dict.DeleteDictReq
	(*DeleteDictResp)(nil),         // 8: dict.DeleteDictResp
	(*GetDictReq)(nil),             // 9: dict.GetDictReq
	(*GetDictResp)(nil),            // 10: dict.GetDictResp
	(*ListDictReq)(nil),            // 11: dict.ListDictReq
	(*ListDictResp)(nil),           // 12: dict.ListDictResp
	(*CreateDictCategoryReq)(nil),  // 13: dict.CreateDictCategoryReq
	(*CreateDictCategoryResp)(nil), // 14: dict.CreateDictCategoryResp
	(*UpdateDictCategoryReq)(nil),  // 15: dict.UpdateDictCategoryReq
	(*UpdateDictCategoryResp)(nil), // 16: dict.UpdateDictCategoryResp
	(*DeleteDictCategoryReq)(nil),  // 17: dict.DeleteDictCategoryReq
	(*DeleteDictCategoryResp)(nil), // 18: dict.DeleteDictCategoryResp
	(*GetDictCategoryReq)(nil),     // 19: dict.GetDictCategoryReq
	(*GetDictCategoryResp)(nil),    // 20: dict.GetDictCategoryResp
	(*ListDictCategoryReq)(nil),    // 21: dict.ListDictCategoryReq
	(*ListDictCategoryResp)(nil),   // 22: dict.ListDictCategoryResp
	(*CreateDictItemReq)(nil),      // 23: dict.CreateDictItemReq
	(*CreateDictItemResp)(nil),     // 24: dict.CreateDictItemResp
	(*UpdateDictItemReq)(nil),      // 25: dict.UpdateDictItemReq
	(*UpdateDictItemResp)(nil),     // 26: dict.UpdateDictItemResp
	(*DeleteDictItemReq)(nil),      // 27: dict.DeleteDictItemReq
	(*DeleteDictItemResp)(nil),     // 28: dict.DeleteDictItemResp
	(*GetDictItemReq)(nil),         // 29: dict.GetDictItemReq
	(*GetDictItemResp)(nil),        // 30: dict.GetDictItemResp
	(*ListDictItemReq)(nil),        // 31: dict.ListDictItemReq
	(*ListDictItemResp)(nil),       // 32: dict.ListDictItemResp
}
var file_dict_proto_depIdxs = []int32{
	0,  // 0: dict.GetDictResp.dict:type_name -> dict.Dict
	0,  // 1: dict.ListDictResp.list:type_name -> dict.Dict
	1,  // 2: dict.GetDictCategoryResp.category:type_name -> dict.DictCategory
	1,  // 3: dict.ListDictCategoryResp.list:type_name -> dict.DictCategory
	2,  // 4: dict.GetDictItemResp.item:type_name -> dict.DictItem
	2,  // 5: dict.ListDictItemResp.list:type_name -> dict.DictItem
	3,  // 6: dict.DictSystemService.CreateDict:input_type -> dict.CreateDictReq
	5,  // 7: dict.DictSystemService.UpdateDict:input_type -> dict.UpdateDictReq
	7,  // 8: dict.DictSystemService.DeleteDict:input_type -> dict.DeleteDictReq
	9,  // 9: dict.DictSystemService.GetDict:input_type -> dict.GetDictReq
	11, // 10: dict.DictSystemService.ListDict:input_type -> dict.ListDictReq
	13, // 11: dict.DictSystemService.CreateDictCategory:input_type -> dict.CreateDictCategoryReq
	15, // 12: dict.DictSystemService.UpdateDictCategory:input_type -> dict.UpdateDictCategoryReq
	17, // 13: dict.DictSystemService.DeleteDictCategory:input_type -> dict.DeleteDictCategoryReq
	19, // 14: dict.DictSystemService.GetDictCategory:input_type -> dict.GetDictCategoryReq
	21, // 15: dict.DictSystemService.ListDictCategory:input_type -> dict.ListDictCategoryReq
	23, // 16: dict.DictSystemService.CreateDictItem:input_type -> dict.CreateDictItemReq
	25, // 17: dict.DictSystemService.UpdateDictItem:input_type -> dict.UpdateDictItemReq
	27, // 18: dict.DictSystemService.DeleteDictItem:input_type -> dict.DeleteDictItemReq
	29, // 19: dict.DictSystemService.GetDictItem:input_type -> dict.GetDictItemReq
	31, // 20: dict.DictSystemService.ListDictItem:input_type -> dict.ListDictItemReq
	4,  // 21: dict.DictSystemService.CreateDict:output_type -> dict.CreateDictResp
	6,  // 22: dict.DictSystemService.UpdateDict:output_type -> dict.UpdateDictResp
	8,  // 23: dict.DictSystemService.DeleteDict:output_type -> dict.DeleteDictResp
	10, // 24: dict.DictSystemService.GetDict:output_type -> dict.GetDictResp
	12, // 25: dict.DictSystemService.ListDict:output_type -> dict.ListDictResp
	14, // 26: dict.DictSystemService.CreateDictCategory:output_type -> dict.CreateDictCategoryResp
	16, // 27: dict.DictSystemService.UpdateDictCategory:output_type -> dict.UpdateDictCategoryResp
	18, // 28: dict.DictSystemService.DeleteDictCategory:output_type -> dict.DeleteDictCategoryResp
	20, // 29: dict.DictSystemService.GetDictCategory:output_type -> dict.GetDictCategoryResp
	22, // 30: dict.DictSystemService.ListDictCategory:output_type -> dict.ListDictCategoryResp
	24, // 31: dict.DictSystemService.CreateDictItem:output_type -> dict.CreateDictItemResp
	26, // 32: dict.DictSystemService.UpdateDictItem:output_type -> dict.UpdateDictItemResp
	28, // 33: dict.DictSystemService.DeleteDictItem:output_type -> dict.DeleteDictItemResp
	30, // 34: dict.DictSystemService.GetDictItem:output_type -> dict.GetDictItemResp
	32, // 35: dict.DictSystemService.ListDictItem:output_type -> dict.ListDictItemResp
	21, // [21:36] is the sub-list for method output_type
	6,  // [6:21] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_dict_proto_init() }
func file_dict_proto_init() {
	if File_dict_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dict_proto_rawDesc), len(file_dict_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dict_proto_goTypes,
		DependencyIndexes: file_dict_proto_depIdxs,
		MessageInfos:      file_dict_proto_msgTypes,
	}.Build()
	File_dict_proto = out.File
	file_dict_proto_goTypes = nil
	file_dict_proto_depIdxs = nil
}
