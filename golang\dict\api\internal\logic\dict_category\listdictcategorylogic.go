package dict_category

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 字典分类列表
func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictCategoryLogic) ListDictCategory(req *types.ListDictCategoryReq) (resp *types.ListDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
