package dict_category

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新字典分类
func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictCategoryLogic) UpdateDictCategory(req *types.UpdateDictCategoryReq) (resp *types.UpdateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
