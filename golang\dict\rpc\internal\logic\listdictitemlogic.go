package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictItemLogic {
	return &ListDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典项列表
func (l *ListDictItemLogic) ListDictItem(in *dict.ListDictItemReq) (*dict.ListDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict.ListDictItemResp{}, nil
}
