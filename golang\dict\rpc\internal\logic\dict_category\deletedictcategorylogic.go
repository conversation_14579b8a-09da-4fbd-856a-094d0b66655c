package dict_category

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict.DeleteDictCategoryReq) (*dict.DeleteDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict.DeleteDictCategoryResp{}, nil
}
