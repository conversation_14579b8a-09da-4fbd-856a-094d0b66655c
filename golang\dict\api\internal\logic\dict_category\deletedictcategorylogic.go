package dict_category

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典分类
func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictCategoryLogic) DeleteDictCategory(req *types.DeleteDictCategoryReq) (resp *types.DeleteDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
