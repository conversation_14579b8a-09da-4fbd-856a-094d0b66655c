package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典分类
func (l *UpdateDictCategoryLogic) UpdateDictCategory(in *dict.UpdateDictCategoryReq) (*dict.UpdateDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict.UpdateDictCategoryResp{}, nil
}
