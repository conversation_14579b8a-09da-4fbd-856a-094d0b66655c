package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典
func (l *UpdateDictLogic) UpdateDict(in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	// todo: add your logic here and delete this line

	return &dict.UpdateDictResp{}, nil
}
