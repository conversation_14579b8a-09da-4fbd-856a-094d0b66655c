syntax = "proto3";

package dict;

option go_package = "./dict";

// ===== 基础消息定义 =====

// 字典基本信息
message Dict {
  int64 id = 1;
  string code = 2;
  string name = 3;
  string remark = 4;
  int32 status = 5;
  string created_time = 6;
  string updated_time = 7;
}

// 字典分类信息
message DictCategory {
  int64 id = 1;
  int64 dict_id = 2;
  string name = 3;
  int32 status = 4;
  string created_time = 5;
  string updated_time = 6;
}

// 字典项信息
message DictItem {
  int64 id = 1;
  int64 dict_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  int32 status = 6;
  string created_time = 7;
  string updated_time = 8;
}

// ===== 字典相关请求响应 =====

// 创建字典请求
message CreateDictReq {
  string code = 1;
  string name = 2;
  string remark = 3;
  int32 status = 4;
}

// 创建字典响应
message CreateDictResp {
  int64 id = 1;
  string message = 2;
}

// 更新字典请求
message UpdateDictReq {
  int64 id = 1;
  string code = 2;
  string name = 3;
  string remark = 4;
  int32 status = 5;
}

// 更新字典响应
message UpdateDictResp {
  bool success = 1;
  string message = 2;
}

// 删除字典请求
message DeleteDictReq {
  int64 id = 1;
}

// 删除字典响应
message DeleteDictResp {
  bool success = 1;
  string message = 2;
}

// 获取字典详情请求
message GetDictReq {
  int64 id = 1;
}

// 获取字典详情响应
message GetDictResp {
  Dict dict = 1;
  string message = 2;
}



// 字典列表请求
message ListDictReq {
  int32 page = 1;
  int32 page_size = 2;
  string code = 3; // 模糊查询
  string name = 4; // 模糊查询
  int32 status = 5; // -1:全部, 0:禁用, 1:启用
}

// 字典列表响应
message ListDictResp {
  int64 total = 1;
  repeated Dict list = 2;
  string message = 3;
}

// ===== 字典分类相关请求响应 =====

// 创建字典分类请求
message CreateDictCategoryReq {
  int64 dict_id = 1;
  string name = 2;
  int32 status = 3;
}

// 创建字典分类响应
message CreateDictCategoryResp {
  int64 id = 1;
  string message = 2;
}

// 更新字典分类请求
message UpdateDictCategoryReq {
  int64 id = 1;
  int64 dict_id = 2;
  string name = 3;
  int32 status = 4;
}

// 更新字典分类响应
message UpdateDictCategoryResp {
  bool success = 1;
  string message = 2;
}

// 删除字典分类请求
message DeleteDictCategoryReq {
  int64 id = 1;
}

// 删除字典分类响应
message DeleteDictCategoryResp {
  bool success = 1;
  string message = 2;
}

// 获取字典分类详情请求
message GetDictCategoryReq {
  int64 id = 1;
}

// 获取字典分类详情响应
message GetDictCategoryResp {
  DictCategory category = 1;
  string message = 2;
}



// 字典分类列表请求
message ListDictCategoryReq {
  int32 page = 1;
  int32 page_size = 2;
  string name = 3; // 模糊查询
  int32 status = 4; // -1:全部, 0:禁用, 1:启用
  int64 dict_id = 5; // 指定字典ID
}

// 字典分类列表响应
message ListDictCategoryResp {
  int64 total = 1;
  repeated DictCategory list = 2;
  string message = 3;
}

// ===== 字典项相关请求响应 =====

// 创建字典项请求
message CreateDictItemReq {
  int64 dict_id = 1;
  int64 category_id = 2;
  string code = 3;
  string name = 4;
  int32 status = 5;
}

// 创建字典项响应
message CreateDictItemResp {
  int64 id = 1;
  string message = 2;
}



// 更新字典项请求
message UpdateDictItemReq {
  int64 id = 1;
  int64 dict_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  int32 status = 6;
}

// 更新字典项响应
message UpdateDictItemResp {
  bool success = 1;
  string message = 2;
}

// 删除字典项请求
message DeleteDictItemReq {
  int64 id = 1;
}

// 删除字典项响应
message DeleteDictItemResp {
  bool success = 1;
  string message = 2;
}

// 获取字典项详情请求
message GetDictItemReq {
  int64 id = 1;
}

// 获取字典项详情响应
message GetDictItemResp {
  DictItem item = 1;
  string message = 2;
}



// 字典项列表请求
message ListDictItemReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 dict_id = 3; // 指定字典ID
  int64 category_id = 4; // 指定分类ID
  string code = 5; // 模糊查询
  string name = 6; // 模糊查询
  int32 status = 7; // -1:全部, 0:禁用, 1:启用
}

// 字典项列表响应
message ListDictItemResp {
  int64 total = 1;
  repeated DictItem list = 2;
  string message = 3;
}

// ===== 服务定义 =====

// 字典系统服务
service DictSystemService {
  // ===== 字典管理 =====
  // 创建字典
  rpc CreateDict(CreateDictReq) returns(CreateDictResp);
  // 更新字典
  rpc UpdateDict(UpdateDictReq) returns(UpdateDictResp);
  // 删除字典
  rpc DeleteDict(DeleteDictReq) returns(DeleteDictResp);
  // 获取字典详情
  rpc GetDict(GetDictReq) returns(GetDictResp);
  // 字典列表
  rpc ListDict(ListDictReq) returns(ListDictResp);

  // ===== 字典分类管理 =====
  // 创建字典分类
  rpc CreateDictCategory(CreateDictCategoryReq) returns(CreateDictCategoryResp);
  // 更新字典分类
  rpc UpdateDictCategory(UpdateDictCategoryReq) returns(UpdateDictCategoryResp);
  // 删除字典分类
  rpc DeleteDictCategory(DeleteDictCategoryReq) returns(DeleteDictCategoryResp);
  // 获取字典分类详情
  rpc GetDictCategory(GetDictCategoryReq) returns(GetDictCategoryResp);
  // 字典分类列表
  rpc ListDictCategory(ListDictCategoryReq) returns(ListDictCategoryResp);

  // ===== 字典项管理 =====
  // 创建字典项
  rpc CreateDictItem(CreateDictItemReq) returns(CreateDictItemResp);
  // 更新字典项
  rpc UpdateDictItem(UpdateDictItemReq) returns(UpdateDictItemResp);
  // 删除字典项
  rpc DeleteDictItem(DeleteDictItemReq) returns(DeleteDictItemResp);
  // 获取字典项详情
  rpc GetDictItem(GetDictItemReq) returns(GetDictItemResp);
  // 字典项列表
  rpc ListDictItem(ListDictItemReq) returns(ListDictItemResp);
} 