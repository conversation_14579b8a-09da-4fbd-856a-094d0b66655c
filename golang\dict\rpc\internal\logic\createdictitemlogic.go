package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ===== 字典项管理 =====
func (l *CreateDictItemLogic) CreateDictItem(in *dict.CreateDictItemReq) (*dict.CreateDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict.CreateDictItemResp{}, nil
}
