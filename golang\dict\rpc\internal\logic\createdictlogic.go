package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ===== 字典管理 =====
func (l *CreateDictLogic) CreateDict(in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	// todo: add your logic here and delete this line

	return &dict.CreateDictResp{}, nil
}
