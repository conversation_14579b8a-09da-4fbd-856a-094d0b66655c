package dict_item

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictItemLogic {
	return &UpdateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典项
func (l *UpdateDictItemLogic) UpdateDictItem(in *dict.UpdateDictItemReq) (*dict.UpdateDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict.UpdateDictItemResp{}, nil
}
