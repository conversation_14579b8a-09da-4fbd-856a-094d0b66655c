package dict_category

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ===== 字典分类管理 =====
func (l *CreateDictCategoryLogic) CreateDictCategory(in *dict.CreateDictCategoryReq) (*dict.CreateDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict.CreateDictCategoryResp{}, nil
}
