package dict_category

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典分类
func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictCategoryLogic) CreateDictCategory(req *types.CreateDictCategoryReq) (resp *types.CreateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
