// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict.proto

package server

import (
	"context"

	"rpc/dict"
	"rpc/internal/logic"
	"rpc/internal/svc"
)

type DictSystemServiceServer struct {
	svcCtx *svc.ServiceContext
	dict.UnimplementedDictSystemServiceServer
}

func NewDictSystemServiceServer(svcCtx *svc.ServiceContext) *DictSystemServiceServer {
	return &DictSystemServiceServer{
		svcCtx: svcCtx,
	}
}

// ===== 字典管理 =====
func (s *DictSystemServiceServer) CreateDict(ctx context.Context, in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	l := logic.NewCreateDictLogic(ctx, s.svcCtx)
	return l.CreateDict(in)
}

// 更新字典
func (s *DictSystemServiceServer) UpdateDict(ctx context.Context, in *dict.UpdateDictReq) (*dict.UpdateDictResp, error) {
	l := logic.NewUpdateDictLogic(ctx, s.svcCtx)
	return l.UpdateDict(in)
}

// 删除字典
func (s *DictSystemServiceServer) DeleteDict(ctx context.Context, in *dict.DeleteDictReq) (*dict.DeleteDictResp, error) {
	l := logic.NewDeleteDictLogic(ctx, s.svcCtx)
	return l.DeleteDict(in)
}

// 获取字典详情
func (s *DictSystemServiceServer) GetDict(ctx context.Context, in *dict.GetDictReq) (*dict.GetDictResp, error) {
	l := logic.NewGetDictLogic(ctx, s.svcCtx)
	return l.GetDict(in)
}

// 字典列表
func (s *DictSystemServiceServer) ListDict(ctx context.Context, in *dict.ListDictReq) (*dict.ListDictResp, error) {
	l := logic.NewListDictLogic(ctx, s.svcCtx)
	return l.ListDict(in)
}

// ===== 字典分类管理 =====
func (s *DictSystemServiceServer) CreateDictCategory(ctx context.Context, in *dict.CreateDictCategoryReq) (*dict.CreateDictCategoryResp, error) {
	l := logic.NewCreateDictCategoryLogic(ctx, s.svcCtx)
	return l.CreateDictCategory(in)
}

// 更新字典分类
func (s *DictSystemServiceServer) UpdateDictCategory(ctx context.Context, in *dict.UpdateDictCategoryReq) (*dict.UpdateDictCategoryResp, error) {
	l := logic.NewUpdateDictCategoryLogic(ctx, s.svcCtx)
	return l.UpdateDictCategory(in)
}

// 删除字典分类
func (s *DictSystemServiceServer) DeleteDictCategory(ctx context.Context, in *dict.DeleteDictCategoryReq) (*dict.DeleteDictCategoryResp, error) {
	l := logic.NewDeleteDictCategoryLogic(ctx, s.svcCtx)
	return l.DeleteDictCategory(in)
}

// 获取字典分类详情
func (s *DictSystemServiceServer) GetDictCategory(ctx context.Context, in *dict.GetDictCategoryReq) (*dict.GetDictCategoryResp, error) {
	l := logic.NewGetDictCategoryLogic(ctx, s.svcCtx)
	return l.GetDictCategory(in)
}

// 字典分类列表
func (s *DictSystemServiceServer) ListDictCategory(ctx context.Context, in *dict.ListDictCategoryReq) (*dict.ListDictCategoryResp, error) {
	l := logic.NewListDictCategoryLogic(ctx, s.svcCtx)
	return l.ListDictCategory(in)
}

// ===== 字典项管理 =====
func (s *DictSystemServiceServer) CreateDictItem(ctx context.Context, in *dict.CreateDictItemReq) (*dict.CreateDictItemResp, error) {
	l := logic.NewCreateDictItemLogic(ctx, s.svcCtx)
	return l.CreateDictItem(in)
}

// 更新字典项
func (s *DictSystemServiceServer) UpdateDictItem(ctx context.Context, in *dict.UpdateDictItemReq) (*dict.UpdateDictItemResp, error) {
	l := logic.NewUpdateDictItemLogic(ctx, s.svcCtx)
	return l.UpdateDictItem(in)
}

// 删除字典项
func (s *DictSystemServiceServer) DeleteDictItem(ctx context.Context, in *dict.DeleteDictItemReq) (*dict.DeleteDictItemResp, error) {
	l := logic.NewDeleteDictItemLogic(ctx, s.svcCtx)
	return l.DeleteDictItem(in)
}

// 获取字典项详情
func (s *DictSystemServiceServer) GetDictItem(ctx context.Context, in *dict.GetDictItemReq) (*dict.GetDictItemResp, error) {
	l := logic.NewGetDictItemLogic(ctx, s.svcCtx)
	return l.GetDictItem(in)
}

// 字典项列表
func (s *DictSystemServiceServer) ListDictItem(ctx context.Context, in *dict.ListDictItemReq) (*dict.ListDictItemResp, error) {
	l := logic.NewListDictItemLogic(ctx, s.svcCtx)
	return l.ListDictItem(in)
}
