package svc

import (
	"rpc/internal/config"
	"rpc/model"
)

type ServiceContext struct {
	Config            config.Config
	DictModel         model.DictModel
	DictCategoryModel model.DictCategoryModel
	DictItemModel     model.DictItemModel
}

func NewServiceContext(c config.Config) *ServiceContext {

	return &ServiceContext{
		Config:            c,
		DictModel:         *model.NewDictModel(model.NewDb(c.MySQL.DataSource)),
		DictCategoryModel: *model.NewDictCategoryModel(model.NewDb(c.MySQL.DataSource)),
		DictItemModel:     *model.NewDictItemModel(model.NewDb(c.MySQL.DataSource)),
	}
}
