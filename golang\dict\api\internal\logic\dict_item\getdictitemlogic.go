package dict_item

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典项详情
func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictItemLogic) GetDictItem(req *types.GetDictItemReq) (resp *types.GetDictItemResp, err error) {
	// todo: add your logic here and delete this line

	return
}
