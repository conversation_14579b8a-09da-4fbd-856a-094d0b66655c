// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.0
// source: dict.proto

package dict

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DictSystemService_CreateDict_FullMethodName         = "/dict.DictSystemService/CreateDict"
	DictSystemService_UpdateDict_FullMethodName         = "/dict.DictSystemService/UpdateDict"
	DictSystemService_DeleteDict_FullMethodName         = "/dict.DictSystemService/DeleteDict"
	DictSystemService_GetDict_FullMethodName            = "/dict.DictSystemService/GetDict"
	DictSystemService_ListDict_FullMethodName           = "/dict.DictSystemService/ListDict"
	DictSystemService_CreateDictCategory_FullMethodName = "/dict.DictSystemService/CreateDictCategory"
	DictSystemService_UpdateDictCategory_FullMethodName = "/dict.DictSystemService/UpdateDictCategory"
	DictSystemService_DeleteDictCategory_FullMethodName = "/dict.DictSystemService/DeleteDictCategory"
	DictSystemService_GetDictCategory_FullMethodName    = "/dict.DictSystemService/GetDictCategory"
	DictSystemService_ListDictCategory_FullMethodName   = "/dict.DictSystemService/ListDictCategory"
	DictSystemService_CreateDictItem_FullMethodName     = "/dict.DictSystemService/CreateDictItem"
	DictSystemService_UpdateDictItem_FullMethodName     = "/dict.DictSystemService/UpdateDictItem"
	DictSystemService_DeleteDictItem_FullMethodName     = "/dict.DictSystemService/DeleteDictItem"
	DictSystemService_GetDictItem_FullMethodName        = "/dict.DictSystemService/GetDictItem"
	DictSystemService_ListDictItem_FullMethodName       = "/dict.DictSystemService/ListDictItem"
)

// DictSystemServiceClient is the client API for DictSystemService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 字典系统服务
type DictSystemServiceClient interface {
	// ===== 字典管理 =====
	// 创建字典
	CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error)
	// 更新字典
	UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error)
	// 删除字典
	DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error)
	// 获取字典详情
	GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error)
	// 字典列表
	ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error)
	// ===== 字典分类管理 =====
	// 创建字典分类
	CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error)
	// 更新字典分类
	UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error)
	// 删除字典分类
	DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error)
	// 获取字典分类详情
	GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error)
	// 字典分类列表
	ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error)
	// ===== 字典项管理 =====
	// 创建字典项
	CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error)
	// 更新字典项
	UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error)
	// 删除字典项
	DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error)
	// 获取字典项详情
	GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error)
	// 字典项列表
	ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error)
}

type dictSystemServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDictSystemServiceClient(cc grpc.ClientConnInterface) DictSystemServiceClient {
	return &dictSystemServiceClient{cc}
}

func (c *dictSystemServiceClient) CreateDict(ctx context.Context, in *CreateDictReq, opts ...grpc.CallOption) (*CreateDictResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDictResp)
	err := c.cc.Invoke(ctx, DictSystemService_CreateDict_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) UpdateDict(ctx context.Context, in *UpdateDictReq, opts ...grpc.CallOption) (*UpdateDictResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDictResp)
	err := c.cc.Invoke(ctx, DictSystemService_UpdateDict_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) DeleteDict(ctx context.Context, in *DeleteDictReq, opts ...grpc.CallOption) (*DeleteDictResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDictResp)
	err := c.cc.Invoke(ctx, DictSystemService_DeleteDict_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) GetDict(ctx context.Context, in *GetDictReq, opts ...grpc.CallOption) (*GetDictResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDictResp)
	err := c.cc.Invoke(ctx, DictSystemService_GetDict_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) ListDict(ctx context.Context, in *ListDictReq, opts ...grpc.CallOption) (*ListDictResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDictResp)
	err := c.cc.Invoke(ctx, DictSystemService_ListDict_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) CreateDictCategory(ctx context.Context, in *CreateDictCategoryReq, opts ...grpc.CallOption) (*CreateDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDictCategoryResp)
	err := c.cc.Invoke(ctx, DictSystemService_CreateDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) UpdateDictCategory(ctx context.Context, in *UpdateDictCategoryReq, opts ...grpc.CallOption) (*UpdateDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDictCategoryResp)
	err := c.cc.Invoke(ctx, DictSystemService_UpdateDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) DeleteDictCategory(ctx context.Context, in *DeleteDictCategoryReq, opts ...grpc.CallOption) (*DeleteDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDictCategoryResp)
	err := c.cc.Invoke(ctx, DictSystemService_DeleteDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) GetDictCategory(ctx context.Context, in *GetDictCategoryReq, opts ...grpc.CallOption) (*GetDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDictCategoryResp)
	err := c.cc.Invoke(ctx, DictSystemService_GetDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) ListDictCategory(ctx context.Context, in *ListDictCategoryReq, opts ...grpc.CallOption) (*ListDictCategoryResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDictCategoryResp)
	err := c.cc.Invoke(ctx, DictSystemService_ListDictCategory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) CreateDictItem(ctx context.Context, in *CreateDictItemReq, opts ...grpc.CallOption) (*CreateDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDictItemResp)
	err := c.cc.Invoke(ctx, DictSystemService_CreateDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) UpdateDictItem(ctx context.Context, in *UpdateDictItemReq, opts ...grpc.CallOption) (*UpdateDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateDictItemResp)
	err := c.cc.Invoke(ctx, DictSystemService_UpdateDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) DeleteDictItem(ctx context.Context, in *DeleteDictItemReq, opts ...grpc.CallOption) (*DeleteDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDictItemResp)
	err := c.cc.Invoke(ctx, DictSystemService_DeleteDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) GetDictItem(ctx context.Context, in *GetDictItemReq, opts ...grpc.CallOption) (*GetDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDictItemResp)
	err := c.cc.Invoke(ctx, DictSystemService_GetDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dictSystemServiceClient) ListDictItem(ctx context.Context, in *ListDictItemReq, opts ...grpc.CallOption) (*ListDictItemResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListDictItemResp)
	err := c.cc.Invoke(ctx, DictSystemService_ListDictItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DictSystemServiceServer is the server API for DictSystemService service.
// All implementations must embed UnimplementedDictSystemServiceServer
// for forward compatibility.
//
// 字典系统服务
type DictSystemServiceServer interface {
	// ===== 字典管理 =====
	// 创建字典
	CreateDict(context.Context, *CreateDictReq) (*CreateDictResp, error)
	// 更新字典
	UpdateDict(context.Context, *UpdateDictReq) (*UpdateDictResp, error)
	// 删除字典
	DeleteDict(context.Context, *DeleteDictReq) (*DeleteDictResp, error)
	// 获取字典详情
	GetDict(context.Context, *GetDictReq) (*GetDictResp, error)
	// 字典列表
	ListDict(context.Context, *ListDictReq) (*ListDictResp, error)
	// ===== 字典分类管理 =====
	// 创建字典分类
	CreateDictCategory(context.Context, *CreateDictCategoryReq) (*CreateDictCategoryResp, error)
	// 更新字典分类
	UpdateDictCategory(context.Context, *UpdateDictCategoryReq) (*UpdateDictCategoryResp, error)
	// 删除字典分类
	DeleteDictCategory(context.Context, *DeleteDictCategoryReq) (*DeleteDictCategoryResp, error)
	// 获取字典分类详情
	GetDictCategory(context.Context, *GetDictCategoryReq) (*GetDictCategoryResp, error)
	// 字典分类列表
	ListDictCategory(context.Context, *ListDictCategoryReq) (*ListDictCategoryResp, error)
	// ===== 字典项管理 =====
	// 创建字典项
	CreateDictItem(context.Context, *CreateDictItemReq) (*CreateDictItemResp, error)
	// 更新字典项
	UpdateDictItem(context.Context, *UpdateDictItemReq) (*UpdateDictItemResp, error)
	// 删除字典项
	DeleteDictItem(context.Context, *DeleteDictItemReq) (*DeleteDictItemResp, error)
	// 获取字典项详情
	GetDictItem(context.Context, *GetDictItemReq) (*GetDictItemResp, error)
	// 字典项列表
	ListDictItem(context.Context, *ListDictItemReq) (*ListDictItemResp, error)
	mustEmbedUnimplementedDictSystemServiceServer()
}

// UnimplementedDictSystemServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDictSystemServiceServer struct{}

func (UnimplementedDictSystemServiceServer) CreateDict(context.Context, *CreateDictReq) (*CreateDictResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDict not implemented")
}
func (UnimplementedDictSystemServiceServer) UpdateDict(context.Context, *UpdateDictReq) (*UpdateDictResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDict not implemented")
}
func (UnimplementedDictSystemServiceServer) DeleteDict(context.Context, *DeleteDictReq) (*DeleteDictResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDict not implemented")
}
func (UnimplementedDictSystemServiceServer) GetDict(context.Context, *GetDictReq) (*GetDictResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDict not implemented")
}
func (UnimplementedDictSystemServiceServer) ListDict(context.Context, *ListDictReq) (*ListDictResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDict not implemented")
}
func (UnimplementedDictSystemServiceServer) CreateDictCategory(context.Context, *CreateDictCategoryReq) (*CreateDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDictCategory not implemented")
}
func (UnimplementedDictSystemServiceServer) UpdateDictCategory(context.Context, *UpdateDictCategoryReq) (*UpdateDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDictCategory not implemented")
}
func (UnimplementedDictSystemServiceServer) DeleteDictCategory(context.Context, *DeleteDictCategoryReq) (*DeleteDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDictCategory not implemented")
}
func (UnimplementedDictSystemServiceServer) GetDictCategory(context.Context, *GetDictCategoryReq) (*GetDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDictCategory not implemented")
}
func (UnimplementedDictSystemServiceServer) ListDictCategory(context.Context, *ListDictCategoryReq) (*ListDictCategoryResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDictCategory not implemented")
}
func (UnimplementedDictSystemServiceServer) CreateDictItem(context.Context, *CreateDictItemReq) (*CreateDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDictItem not implemented")
}
func (UnimplementedDictSystemServiceServer) UpdateDictItem(context.Context, *UpdateDictItemReq) (*UpdateDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDictItem not implemented")
}
func (UnimplementedDictSystemServiceServer) DeleteDictItem(context.Context, *DeleteDictItemReq) (*DeleteDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDictItem not implemented")
}
func (UnimplementedDictSystemServiceServer) GetDictItem(context.Context, *GetDictItemReq) (*GetDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDictItem not implemented")
}
func (UnimplementedDictSystemServiceServer) ListDictItem(context.Context, *ListDictItemReq) (*ListDictItemResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDictItem not implemented")
}
func (UnimplementedDictSystemServiceServer) mustEmbedUnimplementedDictSystemServiceServer() {}
func (UnimplementedDictSystemServiceServer) testEmbeddedByValue()                           {}

// UnsafeDictSystemServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DictSystemServiceServer will
// result in compilation errors.
type UnsafeDictSystemServiceServer interface {
	mustEmbedUnimplementedDictSystemServiceServer()
}

func RegisterDictSystemServiceServer(s grpc.ServiceRegistrar, srv DictSystemServiceServer) {
	// If the following call pancis, it indicates UnimplementedDictSystemServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DictSystemService_ServiceDesc, srv)
}

func _DictSystemService_CreateDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).CreateDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_CreateDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).CreateDict(ctx, req.(*CreateDictReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_UpdateDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).UpdateDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_UpdateDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).UpdateDict(ctx, req.(*UpdateDictReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_DeleteDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).DeleteDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_DeleteDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).DeleteDict(ctx, req.(*DeleteDictReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_GetDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).GetDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_GetDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).GetDict(ctx, req.(*GetDictReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_ListDict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDictReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).ListDict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_ListDict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).ListDict(ctx, req.(*ListDictReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_CreateDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).CreateDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_CreateDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).CreateDictCategory(ctx, req.(*CreateDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_UpdateDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).UpdateDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_UpdateDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).UpdateDictCategory(ctx, req.(*UpdateDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_DeleteDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).DeleteDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_DeleteDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).DeleteDictCategory(ctx, req.(*DeleteDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_GetDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).GetDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_GetDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).GetDictCategory(ctx, req.(*GetDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_ListDictCategory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDictCategoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).ListDictCategory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_ListDictCategory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).ListDictCategory(ctx, req.(*ListDictCategoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_CreateDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).CreateDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_CreateDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).CreateDictItem(ctx, req.(*CreateDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_UpdateDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).UpdateDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_UpdateDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).UpdateDictItem(ctx, req.(*UpdateDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_DeleteDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).DeleteDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_DeleteDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).DeleteDictItem(ctx, req.(*DeleteDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_GetDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).GetDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_GetDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).GetDictItem(ctx, req.(*GetDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DictSystemService_ListDictItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDictItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DictSystemServiceServer).ListDictItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DictSystemService_ListDictItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DictSystemServiceServer).ListDictItem(ctx, req.(*ListDictItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DictSystemService_ServiceDesc is the grpc.ServiceDesc for DictSystemService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DictSystemService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dict.DictSystemService",
	HandlerType: (*DictSystemServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDict",
			Handler:    _DictSystemService_CreateDict_Handler,
		},
		{
			MethodName: "UpdateDict",
			Handler:    _DictSystemService_UpdateDict_Handler,
		},
		{
			MethodName: "DeleteDict",
			Handler:    _DictSystemService_DeleteDict_Handler,
		},
		{
			MethodName: "GetDict",
			Handler:    _DictSystemService_GetDict_Handler,
		},
		{
			MethodName: "ListDict",
			Handler:    _DictSystemService_ListDict_Handler,
		},
		{
			MethodName: "CreateDictCategory",
			Handler:    _DictSystemService_CreateDictCategory_Handler,
		},
		{
			MethodName: "UpdateDictCategory",
			Handler:    _DictSystemService_UpdateDictCategory_Handler,
		},
		{
			MethodName: "DeleteDictCategory",
			Handler:    _DictSystemService_DeleteDictCategory_Handler,
		},
		{
			MethodName: "GetDictCategory",
			Handler:    _DictSystemService_GetDictCategory_Handler,
		},
		{
			MethodName: "ListDictCategory",
			Handler:    _DictSystemService_ListDictCategory_Handler,
		},
		{
			MethodName: "CreateDictItem",
			Handler:    _DictSystemService_CreateDictItem_Handler,
		},
		{
			MethodName: "UpdateDictItem",
			Handler:    _DictSystemService_UpdateDictItem_Handler,
		},
		{
			MethodName: "DeleteDictItem",
			Handler:    _DictSystemService_DeleteDictItem_Handler,
		},
		{
			MethodName: "GetDictItem",
			Handler:    _DictSystemService_GetDictItem_Handler,
		},
		{
			MethodName: "ListDictItem",
			Handler:    _DictSystemService_ListDictItem_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dict.proto",
}
