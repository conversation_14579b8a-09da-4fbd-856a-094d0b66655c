package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典分类列表
func (l *ListDictCategoryLogic) ListDictCategory(in *dict.ListDictCategoryReq) (*dict.ListDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict.ListDictCategoryResp{}, nil
}
