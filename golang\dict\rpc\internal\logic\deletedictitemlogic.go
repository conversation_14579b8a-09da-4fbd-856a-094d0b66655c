package logic

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典项
func (l *DeleteDictItemLogic) DeleteDictItem(in *dict.DeleteDictItemReq) (*dict.DeleteDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict.DeleteDictItemResp{}, nil
}
