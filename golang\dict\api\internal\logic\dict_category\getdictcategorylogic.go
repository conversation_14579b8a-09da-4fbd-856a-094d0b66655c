package dict_category

import (
	"context"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典分类详情
func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictCategoryLogic) GetDictCategory(req *types.GetDictCategoryReq) (resp *types.GetDictCategoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
