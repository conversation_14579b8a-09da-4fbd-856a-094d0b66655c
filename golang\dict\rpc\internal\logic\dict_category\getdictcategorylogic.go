package dict_category

import (
	"context"

	"rpc/dict"
	"rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典分类详情
func (l *GetDictCategoryLogic) GetDictCategory(in *dict.GetDictCategoryReq) (*dict.GetDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict.GetDictCategoryResp{}, nil
}
